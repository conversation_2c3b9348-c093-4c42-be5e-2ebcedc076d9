{"HC-SR04_Interface": {"functions": [{"name": "hcsr04_init", "description": "Initializes the HC-SR04 sensor with the specified trigger and echo pins.", "return_type": "int", "parameters": [{"name": "trigger_pin", "type": "int"}, {"name": "echo_pin", "type": "int"}]}, {"name": "hcsr04_read_distance_cm", "description": "Returns the measured distance in centimeters. IMPLEMENTATION REQUIRED: 1) Send 10μs HIGH pulse on trigger pin 2) Wait for echo pin HIGH 3) Measure echo pulse duration 4) Calculate distance=(duration*0.0343)/2. Returns NAN on timeout/failure.", "return_type": "float", "parameters": []}, {"name": "hcsr04_read_distance_mm", "description": "Returns the measured distance in millimeters.", "return_type": "float", "parameters": []}, {"name": "hcsr04_set_timeout_us", "description": "Sets the echo pulse timeout in microseconds to avoid infinite waits.", "return_type": "void", "parameters": [{"name": "timeout_us", "type": "uint32_t"}]}], "implementation_template": {"critical_steps": ["1. Generate 10μs trigger pulse: gpio_set_level(trig, 1); delay_us(10); gpio_set_level(trig, 0);", "2. Wait for echo HIGH with timeout check", "3. Record start time when echo goes HIGH", "4. Wait for echo LOW with timeout check", "5. Calculate duration = end_time - start_time", "6. Return distance = (duration * 0.0343) / 2.0"], "common_errors": ["Missing trigger pulse generation", "Incorrect timeout logic (start=0 causes immediate timeout)", "Not checking for NAN before JSON serialization"]}}}